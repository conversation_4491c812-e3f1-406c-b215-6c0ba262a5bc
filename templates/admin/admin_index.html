<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - {{ .page_title }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <div class="w-64 bg-gray-800 text-white">
            <div class="p-4 border-b border-gray-700">
                <h1 class="text-xl font-bold">管理后台</h1>
            </div>
            <nav class="mt-4">
                <a href="/admin" class="block py-2 px-4 {{ if eq .active_menu "home" }}bg-gray-900{{ else }}hover:bg-gray-700{{ end }}">首页</a>

                <!-- 栏目列表菜单 -->
                <a href="/admin/category" class="block py-2 px-4 {{ if eq .active_menu "category" }}bg-gray-900{{ else }}hover:bg-gray-700{{ end }}">栏目列表</a>

                <!-- 采集管理菜单 -->
                <a href="/admin/collector" class="block py-2 px-4 {{ if eq .active_menu "collector" }}bg-gray-900{{ else }}hover:bg-gray-700{{ end }}">视频采集</a>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="flex-1 overflow-auto">
            <!-- 顶部导航 -->
            <header class="bg-white shadow">
                <div class="flex justify-between items-center px-6 py-4">
                    <h2 class="text-xl font-semibold">{{ .page_title }}</h2>
                    <div class="flex items-center">
                        <span class="mr-4">欢迎，{{ .admin.Username }}</span>
                        <a href="/admin/logout" class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm">退出登录</a>
                    </div>
                </div>
            </header>

            <!-- 内容 -->
            <main class="p-6">
                {{ if eq .active_menu "home" }}
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold mb-4">欢迎进入管理后台</h3>
                        <p>这是一个简单的后台管理系统，您可以在这里管理您的网站内容。</p>
                    </div>
                {{ else if eq .active_menu "collector" }}
                    {{ template "collector-content" . }}
                {{ else if eq .active_menu "category" }}
                    {{ template "category-content" . }}
                {{ else if eq .active_menu "category-videos" }}
                    {{ template "category-videos-content" . }}
                {{ else if eq .active_menu "video-detail" }}
                    {{ template "video-detail-content" . }}
                {{ end }}
            </main>
        </div>
    </div>
</body>
</html>
