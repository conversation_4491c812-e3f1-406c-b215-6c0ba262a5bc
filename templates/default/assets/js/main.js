// 主JavaScript文件
document.addEventListener('DOMContentLoaded', function() {
    // 图片懒加载
    const images = document.querySelectorAll('img[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }

    // 搜索框功能增强
    const searchInput = document.querySelector('.search input[name="keyword"]');
    if (searchInput) {
        // 搜索建议功能（可以后续扩展）
        searchInput.addEventListener('input', function() {
            const keyword = this.value.trim();
            if (keyword.length > 2) {
                // 这里可以添加搜索建议的AJAX请求
                console.log('搜索关键词:', keyword);
            }
        });

        // 回车搜索
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const form = this.closest('form');
                if (form) {
                    form.submit();
                }
            }
        });
    }

    // 视频卡片悬停效果增强
    const videoItems = document.querySelectorAll('.video-item');
    videoItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // 导航菜单响应式处理
    const nav = document.querySelector('.nav');
    const navToggle = document.createElement('button');
    navToggle.className = 'nav-toggle';
    navToggle.innerHTML = '☰';
    navToggle.style.display = 'none';
    navToggle.style.background = 'none';
    navToggle.style.border = 'none';
    navToggle.style.fontSize = '20px';
    navToggle.style.cursor = 'pointer';

    if (nav) {
        nav.parentNode.insertBefore(navToggle, nav);
        
        navToggle.addEventListener('click', function() {
            const navUl = nav.querySelector('ul');
            if (navUl.style.display === 'none' || navUl.style.display === '') {
                navUl.style.display = 'flex';
            } else {
                navUl.style.display = 'none';
            }
        });
    }

    // 响应式导航处理
    function handleResize() {
        if (window.innerWidth <= 768) {
            navToggle.style.display = 'block';
            const navUl = nav?.querySelector('ul');
            if (navUl) {
                navUl.style.display = 'none';
                navUl.style.flexDirection = 'column';
                navUl.style.position = 'absolute';
                navUl.style.top = '100%';
                navUl.style.left = '0';
                navUl.style.right = '0';
                navUl.style.background = '#fff';
                navUl.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                navUl.style.padding = '10px 0';
            }
        } else {
            navToggle.style.display = 'none';
            const navUl = nav?.querySelector('ul');
            if (navUl) {
                navUl.style.display = 'flex';
                navUl.style.flexDirection = 'row';
                navUl.style.position = 'static';
                navUl.style.background = 'transparent';
                navUl.style.boxShadow = 'none';
                navUl.style.padding = '0';
            }
        }
    }

    window.addEventListener('resize', handleResize);
    handleResize(); // 初始调用

    // 平滑滚动到顶部
    function scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // 添加返回顶部按钮
    const backToTop = document.createElement('button');
    backToTop.innerHTML = '↑';
    backToTop.className = 'back-to-top';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: #3498db;
        color: #fff;
        border: none;
        border-radius: 50%;
        font-size: 20px;
        cursor: pointer;
        display: none;
        z-index: 1000;
        transition: all 0.3s;
    `;

    document.body.appendChild(backToTop);

    backToTop.addEventListener('click', scrollToTop);

    // 显示/隐藏返回顶部按钮
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTop.style.display = 'block';
        } else {
            backToTop.style.display = 'none';
        }
    });

    // 图片加载错误处理
    const allImages = document.querySelectorAll('img');
    allImages.forEach(img => {
        img.addEventListener('error', function() {
            this.src = '/assets/images/no-image.jpg';
            this.alt = '图片加载失败';
        });
    });

    // 表单验证增强
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.style.borderColor = '#e74c3c';
                } else {
                    field.style.borderColor = '#ddd';
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('请填写所有必填字段');
            }
        });
    });

    console.log('前端页面初始化完成');
});
