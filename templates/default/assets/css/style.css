/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
}

.logo a {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    text-decoration: none;
}

.nav {
    display: flex;
    gap: 30px;
}

.nav-item {
    color: #666;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s;
}

.nav-item:hover {
    background: #3498db;
    color: #fff;
}

.search-form {
    display: flex;
    gap: 10px;
}

.search-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 200px;
}

.search-btn {
    padding: 8px 16px;
    background: #3498db;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.search-btn:hover {
    background: #2980b9;
}

/* 主要内容 */
.main {
    min-height: calc(100vh - 120px);
    padding: 20px 0;
}

/* 首页样式 */
.banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    text-align: center;
    padding: 60px 0;
    margin-bottom: 40px;
    border-radius: 8px;
}

.banner-content h1 {
    font-size: 36px;
    margin-bottom: 10px;
}

.banner-content p {
    font-size: 18px;
    opacity: 0.9;
}

.type-section {
    margin-bottom: 40px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

.section-title {
    font-size: 24px;
    color: #2c3e50;
}

.more-link {
    color: #3498db;
    text-decoration: none;
    font-size: 14px;
}

.more-link:hover {
    text-decoration: underline;
}

/* 视频网格 */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.video-item {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.video-item:hover {
    transform: translateY(-5px);
}

.video-thumb {
    position: relative;
}

.thumb-img {
    width: 100%;
    height: 280px;
    object-fit: cover;
}

.thumb-placeholder {
    width: 100%;
    height: 280px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
}

.video-info {
    padding: 15px;
}

.video-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #2c3e50;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.video-meta {
    display: flex;
    gap: 10px;
    font-size: 12px;
    color: #666;
}

.video-year, .video-remarks {
    background: #ecf0f1;
    padding: 2px 6px;
    border-radius: 3px;
}

/* 列表页样式 */
.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-title {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 10px;
}

.type-info, .search-info {
    color: #666;
    font-size: 14px;
}

.video-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.video-item-list {
    display: flex;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.video-item-list .video-thumb {
    flex-shrink: 0;
    width: 200px;
}

.video-item-list .thumb-img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.video-item-list .thumb-placeholder {
    width: 100%;
    height: 150px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
}

.video-details {
    flex: 1;
    padding: 20px;
}

.video-details .video-title {
    font-size: 18px;
    margin-bottom: 10px;
    white-space: normal;
}

.video-details .video-title a {
    color: #2c3e50;
    text-decoration: none;
}

.video-details .video-title a:hover {
    color: #3498db;
}

.video-desc {
    color: #666;
    margin: 10px 0;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.video-actions {
    margin-top: 15px;
}

.btn-watch {
    background: #3498db;
    color: #fff;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    transition: background 0.3s;
}

.btn-watch:hover {
    background: #2980b9;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 40px;
}

.page-btn {
    padding: 8px 12px;
    background: #fff;
    color: #666;
    text-decoration: none;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.3s;
}

.page-btn:hover {
    background: #3498db;
    color: #fff;
    border-color: #3498db;
}

.page-btn.current {
    background: #3498db;
    color: #fff;
    border-color: #3498db;
}

/* 详情页样式 */
.video-detail {
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.video-main {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
}

.video-poster {
    flex-shrink: 0;
    width: 300px;
}

.poster-img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 8px;
}

.poster-placeholder {
    width: 100%;
    height: 400px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    border-radius: 8px;
}

.video-info {
    flex: 1;
}

.video-detail .video-title {
    font-size: 32px;
    color: #2c3e50;
    margin-bottom: 20px;
}

.meta-item {
    display: flex;
    margin-bottom: 10px;
}

.meta-label {
    width: 80px;
    color: #666;
    font-weight: bold;
}

.meta-value {
    flex: 1;
    color: #333;
}

.meta-value a {
    color: #3498db;
    text-decoration: none;
}

.meta-value a:hover {
    text-decoration: underline;
}

.btn-play {
    background: #e74c3c;
    color: #fff;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: bold;
    display: inline-block;
    margin-top: 20px;
    transition: background 0.3s;
}

.btn-play:hover {
    background: #c0392b;
}

.btn-disabled {
    background: #bdc3c7;
    color: #fff;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    display: inline-block;
    margin-top: 20px;
}

.video-description {
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.video-description h3 {
    font-size: 20px;
    color: #2c3e50;
    margin-bottom: 15px;
}

.description-content {
    line-height: 1.8;
    color: #666;
}

.related-videos {
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 错误和空状态 */
.error-message {
    background: #e74c3c;
    color: #fff;
    padding: 15px;
    border-radius: 4px;
    text-align: center;
    margin-bottom: 20px;
}

.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

/* 底部 */
.footer {
    background: #2c3e50;
    color: #fff;
    text-align: center;
    padding: 20px 0;
    margin-top: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .nav {
        flex-wrap: wrap;
        justify-content: center;
    }

    .video-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }

    .video-item-list {
        flex-direction: column;
    }

    .video-item-list .video-thumb {
        width: 100%;
    }

    .video-main {
        flex-direction: column;
    }

    .video-poster {
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }
}

/* 标签页样式 */
.tabs {
    margin-bottom: 20px;
}

.tab-nav {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 15px;
}

.tab-nav-item {
    padding: 10px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-nav-item:hover {
    color: #3498db;
}

.tab-nav-item.active {
    color: #3498db;
    border-bottom-color: #3498db;
    font-weight: 500;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}
