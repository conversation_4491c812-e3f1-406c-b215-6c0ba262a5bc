{{template "default/layout.html" .}}

{{define "title"}}{{.type.TypeName}} - 视频网站{{end}}

{{define "breadcrumb"}}
<div class="breadcrumb">
    <a href="/">首页</a> &gt;
    <span>{{.type.TypeName}}</span>
</div>
{{end}}

{{define "content"}}
<div class="list-page">
    <div class="page-header">
        <h1>{{.type.TypeName}}</h1>
        {{if .type.TypeContent}}
        <p class="type-description">{{.type.TypeContent}}</p>
        {{end}}
    </div>

    {{if .videos}}
    <div class="video-grid">
        {{range .videos}}
        <div class="video-item">
            <div class="video-thumb">
                <a href="/detail/{{.VodID}}">
                    {{if .VodPic}}
                    <img src="{{fixImagePath .VodPic}}" alt="{{.VodName}}" loading="lazy">
                    {{else}}
                    <div class="no-image">暂无图片</div>
                    {{end}}
                </a>
            </div>
            <div class="video-info">
                <h4><a href="/detail/{{.VodID}}">{{.VodName}}</a></h4>
                <p class="video-meta">
                    {{if .VodRemarks}}<span class="remarks">{{.VodRemarks}}</span>{{end}}
                    {{if .VodYear}}<span class="year">{{.VodYear}}</span>{{end}}
                    {{if .VodArea}}<span class="area">{{.VodArea}}</span>{{end}}
                </p>
                {{if .VodContent}}
                <p class="video-desc">{{.VodContent}}</p>
                {{end}}
            </div>
        </div>
        {{end}}
    </div>

    <!-- 分页 -->
    {{if gt .pagination.totalPages 1}}
    <div class="pagination">
        {{if gt .pagination.current 1}}
        <a href="/type/{{.current_type_id}}?page=1">&laquo; 首页</a>
        <a href="/type/{{.current_type_id}}?page={{subtract .pagination.current 1}}">&lsaquo; 上一页</a>
        {{end}}

        {{$current := .pagination.current}}
        {{$total := .pagination.totalPages}}
        {{$start := 1}}
        {{$end := $total}}

        {{if gt $total 10}}
            {{if gt $current 5}}
                {{$start = subtract $current 4}}
            {{end}}
            {{if lt (add $current 5) $total}}
                {{$end = add $current 5}}
            {{else}}
                {{$end = $total}}
                {{if gt $total 10}}
                    {{$start = subtract $total 9}}
                {{end}}
            {{end}}
        {{end}}

        {{range $i := $start}}
            {{if le $i $end}}
                {{if eq $i $current}}
                <span class="current">{{$i}}</span>
                {{else}}
                <a href="/type/{{$.current_type_id}}?page={{$i}}">{{$i}}</a>
                {{end}}
            {{end}}
        {{end}}

        {{if lt .pagination.current .pagination.totalPages}}
        <a href="/type/{{.current_type_id}}?page={{add .pagination.current 1}}">下一页 &rsaquo;</a>
        <a href="/type/{{.current_type_id}}?page={{.pagination.totalPages}}">末页 &raquo;</a>
        {{end}}
    </div>

    <div class="pagination-info">
        <p>共 {{.pagination.total}} 个视频，当前第 {{.pagination.current}}/{{.pagination.totalPages}} 页</p>
    </div>
    {{end}}

    {{else}}
    <div class="empty-content">
        <h2>暂无视频</h2>
        <p>该栏目下暂时没有视频内容</p>
        <a href="/" class="back-home">返回首页</a>
    </div>
    {{end}}
</div>
{{end}}

{{define "head"}}
<style>
.list-page {
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.type-description {
    color: #666;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.video-desc {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.pagination-info {
    text-align: center;
    margin-top: 20px;
    color: #666;
    font-size: 14px;
}

.empty-content {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.back-home {
    display: inline-block;
    margin-top: 20px;
    padding: 10px 20px;
    background: #3498db;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    transition: background 0.3s;
}

.back-home:hover {
    background: #2980b9;
}

.no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f5f5f5;
    color: #999;
    font-size: 12px;
}
</style>
{{end}}
