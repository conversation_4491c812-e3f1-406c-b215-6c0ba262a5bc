/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
}

.logo a {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    text-decoration: none;
}

.nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav a {
    color: #666;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s;
}

.nav a:hover,
.nav a.active {
    background: #3498db;
    color: #fff;
}

.search form {
    display: flex;
    gap: 10px;
}

.search input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 200px;
}

.search button {
    padding: 8px 16px;
    background: #3498db;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.search button:hover {
    background: #2980b9;
}

/* 主要内容区域 */
.main {
    min-height: calc(100vh - 140px);
    padding: 30px 0;
}

/* 栏目分区样式 */
.type-section {
    background: #fff;
    margin-bottom: 30px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.section-header h2 {
    color: #2c3e50;
    font-size: 20px;
}

.more-link {
    color: #3498db;
    text-decoration: none;
    font-size: 14px;
}

.more-link:hover {
    color: #2980b9;
}

.section-content {
    padding: 30px;
}

/* 视频列表样式 */
.video-list {
    margin-bottom: 40px;
}

.video-list:last-child {
    margin-bottom: 0;
}

.video-list h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 18px;
    border-left: 4px solid #3498db;
    padding-left: 15px;
}

.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.video-item {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid #eee;
}

.video-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.video-thumb {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 宽高比 */
    overflow: hidden;
}

.video-thumb img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.video-item:hover .video-thumb img {
    transform: scale(1.05);
}

.video-info {
    padding: 15px;
}

.video-info h4 {
    margin-bottom: 8px;
}

.video-info h4 a {
    color: #2c3e50;
    text-decoration: none;
    font-size: 14px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-info h4 a:hover {
    color: #3498db;
}

.video-meta {
    font-size: 12px;
    color: #666;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.video-meta span {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 40px;
    padding: 20px 0;
}

.pagination a,
.pagination span {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #666;
    transition: all 0.3s;
}

.pagination a:hover {
    background: #3498db;
    color: #fff;
    border-color: #3498db;
}

.pagination .current {
    background: #3498db;
    color: #fff;
    border-color: #3498db;
}

/* 底部样式 */
.footer {
    background: #2c3e50;
    color: #fff;
    text-align: center;
    padding: 20px 0;
}

/* 面包屑导航 */
.breadcrumb {
    margin-bottom: 20px;
    padding: 15px 0;
}

.breadcrumb a {
    color: #3498db;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #2980b9;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 15px;
    }

    .nav ul {
        gap: 15px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .search input {
        width: 150px;
    }

    .video-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }

    .section-content {
        padding: 20px;
    }

    .container {
        padding: 0 15px;
    }
}

@media (max-width: 480px) {
    .video-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .section-header {
        padding: 15px 20px;
    }

    .section-header h2 {
        font-size: 18px;
    }
}

/* 无图片占位符样式 */
.no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f5f5f5;
    color: #999;
    font-size: 12px;
    border: 1px dashed #ddd;
}

/* 空内容样式 */
.empty-content {
    text-align: center;
    padding: 60px 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin: 20px 0;
}

.empty-content h2 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.empty-content p {
    color: #666;
    margin-bottom: 20px;
}
