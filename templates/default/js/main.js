// 前端主要JavaScript文件

document.addEventListener('DOMContentLoaded', function() {
    // 图片懒加载
    const images = document.querySelectorAll('img[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }

    // 搜索表单增强
    const searchForms = document.querySelectorAll('.search form');
    searchForms.forEach(form => {
        const input = form.querySelector('input[name="keyword"]');
        if (input) {
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const keyword = this.value.trim();
                    if (keyword === '') {
                        e.preventDefault();
                        alert('请输入搜索关键词');
                        return false;
                    }
                }
            });
        }
    });

    // 移动端导航菜单
    const nav = document.querySelector('.nav');
    if (nav && window.innerWidth <= 768) {
        // 可以在这里添加移动端菜单切换逻辑
    }

    // 视频卡片hover效果增强
    const videoItems = document.querySelectorAll('.video-item');
    videoItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // 剧集播放链接处理
    const episodeLinks = document.querySelectorAll('.episode-item a');
    episodeLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 可以在这里添加播放前的处理逻辑
            console.log('播放剧集:', this.textContent);
        });
    });

    // 返回顶部功能
    const backToTop = document.createElement('div');
    backToTop.innerHTML = '↑';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: #3498db;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        opacity: 0;
        transition: opacity 0.3s;
        z-index: 1000;
    `;
    
    document.body.appendChild(backToTop);

    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
        } else {
            backToTop.style.opacity = '0';
        }
    });

    backToTop.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // 图片加载错误处理
    const allImages = document.querySelectorAll('img');
    allImages.forEach(img => {
        img.addEventListener('error', function() {
            this.src = '/default/images/no-image.jpg';
            this.alt = '图片加载失败';
        });
    });
});

// 工具函数
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// 搜索建议功能（可选）
function initSearchSuggestions() {
    const searchInputs = document.querySelectorAll('input[name="keyword"]');
    
    searchInputs.forEach(input => {
        let timeout;
        
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            const keyword = this.value.trim();
            
            if (keyword.length >= 2) {
                timeout = setTimeout(() => {
                    // 这里可以添加搜索建议的AJAX请求
                    console.log('搜索建议:', keyword);
                }, 300);
            }
        });
    });
}
