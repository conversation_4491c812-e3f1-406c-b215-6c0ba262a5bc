{{ define "content" }}
<div class="list-content">
    {{ if .error }}
    <div class="error-message">{{ .error }}</div>
    {{ else }}

    <!-- 页面标题 -->
    <div class="page-header">
        {{ if .isSearch }}
        <h1 class="page-title">搜索结果：{{ .keyword }}</h1>
        <div class="search-info">共找到 {{ .totalCount }} 个结果</div>
        {{ else if .typeInfo }}
        <h1 class="page-title">{{ .typeInfo.TypeName }}</h1>
        <div class="type-info">共 {{ .totalCount }} 个视频</div>
        {{ end }}
    </div>

    <!-- 视频列表 -->
    {{ if .videos }}
    <div class="video-list">
        {{ range .videos }}
        <div class="video-item-list">
            <div class="video-thumb">
                <a href="/detail/{{ .VodID }}">
                    {{ if .VodPic }}
                    <img src="{{ fixImagePath .VodPic }}" alt="{{ .VodName }}" class="thumb-img">
                    {{ else }}
                    <div class="thumb-placeholder">暂无图片</div>
                    {{ end }}
                </a>
            </div>
            <div class="video-details">
                <h3 class="video-title">
                    <a href="/detail/{{ .VodID }}">{{ .VodName }}</a>
                </h3>
                <div class="video-meta">
                    <span class="video-year">{{ .VodYear }}</span>
                    {{ if .VodArea }}
                    <span class="video-area">{{ .VodArea }}</span>
                    {{ end }}
                    {{ if .VodLang }}
                    <span class="video-lang">{{ .VodLang }}</span>
                    {{ end }}
                    {{ if .VodRemarks }}
                    <span class="video-remarks">{{ .VodRemarks }}</span>
                    {{ end }}
                </div>
                {{ if .VodContent }}
                <div class="video-desc">{{ .VodContent }}</div>
                {{ end }}
                <div class="video-actions">
                    <a href="/detail/{{ .VodID }}" class="btn-watch">立即观看</a>
                </div>
            </div>
        </div>
        {{ end }}
    </div>

    <!-- 分页 -->
    {{ if gt .totalPages 1 }}
    <div class="pagination">
        {{ if gt .page 1 }}
        <a href="?page={{ subtract .page 1 }}{{ if .keyword }}&keyword={{ .keyword }}{{ end }}" class="page-btn">上一页</a>
        {{ end }}

        {{ $currentPage := .page }}
        {{ $totalPages := .totalPages }}
        {{ range $i := 1 }}
        {{ if le $i $totalPages }}
        {{ if eq $i $currentPage }}
        <span class="page-btn current">{{ $i }}</span>
        {{ else }}
        <a href="?page={{ $i }}{{ if $.keyword }}&keyword={{ $.keyword }}{{ end }}" class="page-btn">{{ $i }}</a>
        {{ end }}
        {{ end }}
        {{ end }}

        {{ if lt .page .totalPages }}
        <a href="?page={{ add .page 1 }}{{ if .keyword }}&keyword={{ .keyword }}{{ end }}" class="page-btn">下一页</a>
        {{ end }}
    </div>
    {{ end }}

    {{ else }}
    <div class="no-results">
        {{ if .isSearch }}
        <p>没有找到相关视频，请尝试其他关键词。</p>
        {{ else }}
        <p>该栏目暂无视频。</p>
        {{ end }}
    </div>
    {{ end }}

    {{ end }}
</div>
{{ end }}
