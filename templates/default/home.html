{{ define "content" }}
<div class="home-content">
    {{ if .error }}
    <div class="error-message">{{ .error }}</div>
    {{ else }}

    <!-- 轮播图区域 -->
    <section class="banner">
        <div class="banner-content">
            <h1>欢迎来到视频网站</h1>
            <p>精彩视频，尽在这里</p>
        </div>
    </section>

    <!-- 栏目视频展示 -->
    {{ range .parentTypes }}
    <section class="type-section">
        <div class="section-header">
            <h2 class="section-title">{{ .TypeName }}</h2>
            <a href="/list/{{ .TypeID }}" class="more-link">更多 &raquo;</a>
        </div>

        <div class="video-grid">
            {{ $videos := index $.typeVideos .TypeID }}
            {{ range $videos }}
            <div class="video-item">
                <div class="video-thumb">
                    <a href="/detail/{{ .VodID }}">
                        {{ if .VodPic }}
                        <img src="{{ fixImagePath .VodPic }}" alt="{{ .VodName }}" class="thumb-img">
                        {{ else }}
                        <div class="thumb-placeholder">暂无图片</div>
                        {{ end }}
                        <div class="video-info">
                            <div class="video-title">{{ .VodName }}</div>
                            <div class="video-meta">
                                <span class="video-year">{{ .VodYear }}</span>
                                {{ if .VodRemarks }}
                                <span class="video-remarks">{{ .VodRemarks }}</span>
                                {{ end }}
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            {{ end }}
        </div>
    </section>
    {{ end }}

    {{ end }}
</div>
{{ end }}

{{ template "layout.html" . }}
