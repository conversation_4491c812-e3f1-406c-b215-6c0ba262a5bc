package utils

import (
	"html/template"
	"strings"
)

// GetTemplateFuncs 返回所有模板函数
func GetTemplateFuncs() template.FuncMap {
	return template.FuncMap{
		"add":          Add,
		"subtract":     Subtract,
		"mul":          Multiply,
		"fixImagePath": FixImagePath,
	}
}

// Add 加法函数
func Add(a, b int) int {
	return a + b
}

// Subtract 减法函数
func Subtract(a, b int) int {
	return a - b
}

// Multiply 乘法函数
func Multiply(a, b int) int {
	return a * b
}

// FixImagePath 修复图片路径，将反斜杠替换为正斜杠
func FixImagePath(path string) string {
	if path == "" {
		return ""
	}
	// 将反斜杠替换为正斜杠
	path = strings.ReplaceAll(path, "\\", "/")
	// 确保路径以/开头
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}
	return path
}
