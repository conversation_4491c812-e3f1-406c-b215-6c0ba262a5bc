<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="/">视频网站</a>
                </div>

                <!-- 导航菜单 -->
                <nav class="nav">
                    <a href="/" class="nav-item">首页</a>
                    {{ range .parentTypes }}
                    <a href="/list/{{ .TypeID }}" class="nav-item">{{ .TypeName }}</a>
                    {{ end }}
                </nav>

                <!-- 搜索框 -->
                <div class="search">
                    <form action="/search" method="get" class="search-form">
                        <input type="text" name="keyword" placeholder="搜索视频..." value="{{ .keyword }}" class="search-input">
                        <button type="submit" class="search-btn">搜索</button>
                    </form>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="main">
        <div class="container">
            <div class="home-content">
                {{ if .error }}
                <div class="error-message">{{ .error }}</div>
                {{ else }}

                <!-- 轮播图区域 -->
                <section class="banner">
                    <div class="banner-content">
                        <h1>欢迎来到视频网站</h1>
                        <p>精彩视频，尽在这里</p>
                    </div>
                </section>

                <!-- 栏目视频展示 -->
                {{ range .parentTypes }}
                <section class="type-section">
                    <div class="section-header">
                        <h2 class="section-title">{{ .TypeName }}</h2>
                        <a href="/list/{{ .TypeID }}" class="more-link">更多 &raquo;</a>
                    </div>

                    <div class="video-grid">
                        {{ range $index, $video := index $.typeVideos .TypeID }}
                        {{ if lt $index $limit }}
                        <div class="video-item">
                            <div class="video-thumb">
                                <a href="/detail/{{ $video.VodID }}">
                                    {{ if $video.VodPic }}
                                    <img src="{{ fixImagePath $video.VodPic }}" alt="{{ $video.VodName }}" class="thumb-img">
                                    {{ else }}
                                    <div class="thumb-placeholder">暂无图片</div>
                                    {{ end }}
                                    <div class="video-info">
                                        <div class="video-title">{{ $video.VodName }}</div>
                                        <div class="video-meta">
                                            <span class="video-year">{{ $video.VodYear }}</span>
                                            {{ if $video.VodRemarks }}
                                            <span class="video-remarks">{{ $video.VodRemarks }}</span>
                                            {{ end }}
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        {{ end }}
                        {{ end }}
                    </div>
                </section>
                {{ end }}
                {{ end }}
            </div>
        </div>
    </main>

    <!-- 底部 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2024 视频网站. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
