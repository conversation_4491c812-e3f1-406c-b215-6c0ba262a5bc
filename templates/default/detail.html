{{ define "content" }}
<div class="detail-content">
    {{ if .error }}
    <div class="error-message">{{ .error }}</div>
    {{ else }}

    <!-- 视频详情 -->
    <div class="video-detail">
        <div class="video-main">
            <div class="video-poster">
                {{ if .video.VodPic }}
                <img src="{{ fixImagePath .video.VodPic }}" alt="{{ .video.VodName }}" class="poster-img">
                {{ else }}
                <div class="poster-placeholder">暂无图片</div>
                {{ end }}
            </div>

            <div class="video-info">
                <h1 class="video-title">{{ .video.VodName }}</h1>

                <div class="video-meta">
                    <div class="meta-item">
                        <span class="meta-label">栏目：</span>
                        <span class="meta-value">
                            <a href="/list/{{ .typeInfo.TypeID }}">{{ .typeInfo.TypeName }}</a>
                        </span>
                    </div>
                    {{ if .video.VodYear }}
                    <div class="meta-item">
                        <span class="meta-label">年份：</span>
                        <span class="meta-value">{{ .video.VodYear }}</span>
                    </div>
                    {{ end }}
                    {{ if .video.VodArea }}
                    <div class="meta-item">
                        <span class="meta-label">地区：</span>
                        <span class="meta-value">{{ .video.VodArea }}</span>
                    </div>
                    {{ end }}
                    {{ if .video.VodLang }}
                    <div class="meta-item">
                        <span class="meta-label">语言：</span>
                        <span class="meta-value">{{ .video.VodLang }}</span>
                    </div>
                    {{ end }}
                    {{ if .video.VodDirector }}
                    <div class="meta-item">
                        <span class="meta-label">导演：</span>
                        <span class="meta-value">{{ .video.VodDirector }}</span>
                    </div>
                    {{ end }}
                    {{ if .video.VodActor }}
                    <div class="meta-item">
                        <span class="meta-label">主演：</span>
                        <span class="meta-value">{{ .video.VodActor }}</span>
                    </div>
                    {{ end }}
                    {{ if .video.VodRemarks }}
                    <div class="meta-item">
                        <span class="meta-label">状态：</span>
                        <span class="meta-value">{{ .video.VodRemarks }}</span>
                    </div>
                    {{ end }}
                </div>

                <!-- 播放按钮 -->
                <div class="video-actions">
                    {{ if .video.VodPlayUrl }}
                    <a href="{{ .video.VodPlayUrl }}" target="_blank" class="btn-play">立即播放</a>
                    {{ else }}
                    <span class="btn-disabled">暂无播放地址</span>
                    {{ end }}
                </div>
            </div>
        </div>

        <!-- 视频简介 -->
        {{ if .video.VodContent }}
        <div class="video-description">
            <h3>剧情简介</h3>
            <div class="description-content">{{ .video.VodContent }}</div>
        </div>
        {{ end }}
    </div>

    <!-- 相关视频 -->
    {{ if .relatedVideos }}
    {{/* 模板配置区域 - 您可以在这里修改相关视频显示数量 */}}
    {{ $relatedVideosLimit := 6 }}  {{/* 相关视频显示数量 */}}

    <div class="related-videos">
        <h3 class="section-title">相关推荐</h3>
        <div class="video-grid">
            {{ $count := 0 }}
            {{ range .relatedVideos }}
            {{ if lt $count $relatedVideosLimit }}
            <div class="video-item">
                <div class="video-thumb">
                    <a href="/detail/{{ .VodID }}">
                        {{ if .VodPic }}
                        <img src="{{ fixImagePath .VodPic }}" alt="{{ .VodName }}" class="thumb-img">
                        {{ else }}
                        <div class="thumb-placeholder">暂无图片</div>
                        {{ end }}
                        <div class="video-info">
                            <div class="video-title">{{ .VodName }}</div>
                            <div class="video-meta">
                                <span class="video-year">{{ .VodYear }}</span>
                                {{ if .VodRemarks }}
                                <span class="video-remarks">{{ .VodRemarks }}</span>
                                {{ end }}
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            {{ $count = add $count 1 }}
            {{ end }}
            {{ end }}
        </div>
    </div>
    {{ end }}

    {{ end }}
</div>
{{ end }}
