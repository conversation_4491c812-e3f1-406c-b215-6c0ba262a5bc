<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{block "title" .}}视频网站{{end}}</title>
    <link rel="stylesheet" href="/assets/css/style.css">
    {{block "head" .}}{{end}}
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="/">视频网站</a>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="/" {{if eq .current_page "index"}}class="active"{{end}}>首页</a></li>
                    {{range .parent_types}}
                    <li><a href="/type/{{.TypeID}}" {{if eq .TypeID $.current_type_id}}class="active"{{end}}>{{.TypeName}}</a></li>
                    {{end}}
                </ul>
            </nav>
            <div class="search">
                <form action="/search" method="get">
                    <input type="text" name="keyword" placeholder="搜索视频..." value="{{.keyword}}">
                    <button type="submit">搜索</button>
                </form>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="main">
        <div class="container">
            {{block "breadcrumb" .}}{{end}}
            {{block "content" .}}{{end}}
        </div>
    </main>

    <!-- 底部 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 视频网站. All rights reserved.</p>
        </div>
    </footer>

    <script src="/assets/js/main.js"></script>
    {{block "scripts" .}}{{end}}
</body>
</html>
