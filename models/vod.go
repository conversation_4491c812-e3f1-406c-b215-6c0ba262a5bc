package models

import (
	"time"

	"gorm.io/gorm"
)

// Vod 视频信息模型
type Vod struct {
	VodID            int       `gorm:"column:vod_id;primaryKey;autoIncrement" json:"vod_id"`
	TypeID           int       `gorm:"column:type_id;index" json:"type_id"`
	TypeID1          int       `gorm:"column:type_id_1;index" json:"type_id_1"`
	GroupID          int       `gorm:"column:group_id;default:0" json:"group_id"`
	VodName          string    `gorm:"column:vod_name;size:255;index" json:"vod_name"`
	VodSub           string    `gorm:"column:vod_sub;size:255" json:"vod_sub"`
	VodEn            string    `gorm:"column:vod_en;size:255;index" json:"vod_en"`
	VodStatus        int8      `gorm:"column:vod_status;default:1;index" json:"vod_status"`
	VodLetter        string    `gorm:"column:vod_letter;size:1;index" json:"vod_letter"`
	VodColor         string    `gorm:"column:vod_color;size:6" json:"vod_color"`
	VodTag           string    `gorm:"column:vod_tag;size:100" json:"vod_tag"`
	VodClass         string    `gorm:"column:vod_class;size:255" json:"vod_class"`
	VodPic           string    `gorm:"column:vod_pic;size:255" json:"vod_pic"`
	VodPicThumb      string    `gorm:"column:vod_pic_thumb;size:255" json:"vod_pic_thumb"`
	VodPicSlide      string    `gorm:"column:vod_pic_slide;size:255" json:"vod_pic_slide"`
	VodPicScreenshot string    `gorm:"column:vod_pic_screenshot;type:text" json:"vod_pic_screenshot"`
	VodActor         string    `gorm:"column:vod_actor;size:255;index" json:"vod_actor"`
	VodDirector      string    `gorm:"column:vod_director;size:255;index" json:"vod_director"`
	VodWriter        string    `gorm:"column:vod_writer;size:100" json:"vod_writer"`
	VodBehind        string    `gorm:"column:vod_behind;size:100" json:"vod_behind"`
	VodBlurb         string    `gorm:"column:vod_blurb;type:text" json:"vod_blurb"`
	VodContent       string    `gorm:"column:vod_content;type:text" json:"vod_content"`
	VodArea          string    `gorm:"column:vod_area;size:20;index" json:"vod_area"`
	VodLang          string    `gorm:"column:vod_lang;size:20;index" json:"vod_lang"`
	VodYear          string    `gorm:"column:vod_year;size:10;index" json:"vod_year"`
	VodRemarks       string    `gorm:"column:vod_remarks;size:100" json:"vod_remarks"`
	VodPubdate       string    `gorm:"column:vod_pubdate;size:100" json:"vod_pubdate"`
	VodTotal         int       `gorm:"column:vod_total;default:0" json:"vod_total"`
	VodSerial        string    `gorm:"column:vod_serial;size:20" json:"vod_serial"`
	VodTv            string    `gorm:"column:vod_tv;size:30" json:"vod_tv"`
	VodWeekday       string    `gorm:"column:vod_weekday;size:30" json:"vod_weekday"`
	VodVersion       string    `gorm:"column:vod_version;size:30" json:"vod_version"`
	VodState         string    `gorm:"column:vod_state;size:30" json:"vod_state"`
	VodAuthor        string    `gorm:"column:vod_author;size:60" json:"vod_author"`
	VodJumpurl       string    `gorm:"column:vod_jumpurl;size:150" json:"vod_jumpurl"`
	VodTpl           string    `gorm:"column:vod_tpl;size:30" json:"vod_tpl"`
	VodTplPlay       string    `gorm:"column:vod_tpl_play;size:30" json:"vod_tpl_play"`
	VodTplDown       string    `gorm:"column:vod_tpl_down;size:30" json:"vod_tpl_down"`
	VodIsend         int8      `gorm:"column:vod_isend;default:0" json:"vod_isend"`
	VodLock          int8      `gorm:"column:vod_lock;default:0" json:"vod_lock"`
	VodLevel         int8      `gorm:"column:vod_level;default:0" json:"vod_level"`
	VodCopyright     int8      `gorm:"column:vod_copyright;default:0" json:"vod_copyright"`
	VodPoints        int       `gorm:"column:vod_points;default:0" json:"vod_points"`
	VodPointsPlay    int       `gorm:"column:vod_points_play;default:0" json:"vod_points_play"`
	VodPointsDown    int       `gorm:"column:vod_points_down;default:0" json:"vod_points_down"`
	VodHits          int       `gorm:"column:vod_hits;default:0" json:"vod_hits"`
	VodHitsDay       int       `gorm:"column:vod_hits_day;default:0" json:"vod_hits_day"`
	VodHitsWeek      int       `gorm:"column:vod_hits_week;default:0" json:"vod_hits_week"`
	VodHitsMonth     int       `gorm:"column:vod_hits_month;default:0" json:"vod_hits_month"`
	VodDuration      string    `gorm:"column:vod_duration;size:30" json:"vod_duration"`
	VodUp            int       `gorm:"column:vod_up;default:0" json:"vod_up"`
	VodDown          int       `gorm:"column:vod_down;default:0" json:"vod_down"`
	VodScore         string    `gorm:"column:vod_score;size:10" json:"vod_score"`
	VodScoreAll      int       `gorm:"column:vod_score_all;default:0" json:"vod_score_all"`
	VodScoreNum      int       `gorm:"column:vod_score_num;default:0" json:"vod_score_num"`
	VodTime          time.Time `gorm:"column:vod_time;index" json:"vod_time"`
	VodTimeAdd       int64     `gorm:"column:vod_time_add;index" json:"vod_time_add"`
	VodTimeHits      int64     `gorm:"column:vod_time_hits;default:0" json:"vod_time_hits"`
	VodTimeMake      int64     `gorm:"column:vod_time_make;default:0" json:"vod_time_make"`
	VodTrysee        int       `gorm:"column:vod_trysee;default:0" json:"vod_trysee"`
	VodDoubanId      int       `gorm:"column:vod_douban_id;default:0" json:"vod_douban_id"`
	VodDoubanScore   string    `gorm:"column:vod_douban_score;size:10" json:"vod_douban_score"`
	VodReurl         string    `gorm:"column:vod_reurl;size:255" json:"vod_reurl"`
	VodRelVod        string    `gorm:"column:vod_rel_vod;type:text" json:"vod_rel_vod"`
	VodRelArt        string    `gorm:"column:vod_rel_art;type:text" json:"vod_rel_art"`
	VodPwd           string    `gorm:"column:vod_pwd;size:10" json:"vod_pwd"`
	VodPwdUrl        string    `gorm:"column:vod_pwd_url;size:255" json:"vod_pwd_url"`
	VodPwdPlay       string    `gorm:"column:vod_pwd_play;size:10" json:"vod_pwd_play"`
	VodPwdPlayUrl    string    `gorm:"column:vod_pwd_play_url;size:255" json:"vod_pwd_play_url"`
	VodPwdDown       string    `gorm:"column:vod_pwd_down;size:10" json:"vod_pwd_down"`
	VodPwdDownUrl    string    `gorm:"column:vod_pwd_down_url;size:255" json:"vod_pwd_down_url"`
	VodPlayFrom      string    `gorm:"column:vod_play_from;size:255" json:"vod_play_from"`
	VodPlayServer    string    `gorm:"column:vod_play_server;size:255" json:"vod_play_server"`
	VodPlayNote      string    `gorm:"column:vod_play_note;size:255" json:"vod_play_note"`
	VodPlayUrl       string    `gorm:"column:vod_play_url;type:text" json:"vod_play_url"`
	VodDownFrom      string    `gorm:"column:vod_down_from;size:255" json:"vod_down_from"`
	VodDownServer    string    `gorm:"column:vod_down_server;size:255" json:"vod_down_server"`
	VodDownNote      string    `gorm:"column:vod_down_note;size:255" json:"vod_down_note"`
	VodDownUrl       string    `gorm:"column:vod_down_url;type:text" json:"vod_down_url"`
	VodPlot          int8      `gorm:"column:vod_plot;default:0" json:"vod_plot"`
	VodPlotName      string    `gorm:"column:vod_plot_name;type:text" json:"vod_plot_name"`
	VodPlotDetail    string    `gorm:"column:vod_plot_detail;type:text" json:"vod_plot_detail"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// TableName 设置表名
func (Vod) TableName() string {
	return "vods"
}

// FindVodByID 通过ID查找视频
func FindVodByID(db *gorm.DB, vodID int) (*Vod, error) {
	var vod Vod
	result := db.First(&vod, vodID)
	if result.Error != nil {
		return nil, result.Error
	}
	return &vod, nil
}

// FindVodsByTypeID 查找指定分类下的所有视频
func FindVodsByTypeID(db *gorm.DB, typeID int, page, pageSize int) ([]*Vod, int64, error) {
	var vods []*Vod
	var total int64

	// 查询总数
	if err := db.Model(&Vod{}).Where("type_id = ?", typeID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := db.Where("type_id = ?", typeID).
		Order("vod_time DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&vods)

	if result.Error != nil {
		return nil, 0, result.Error
	}

	return vods, total, nil
}

// CreateVod 创建视频
func CreateVod(db *gorm.DB, vod *Vod) error {
	return db.Create(vod).Error
}

// UpdateVod 更新视频
func UpdateVod(db *gorm.DB, vod *Vod) error {
	return db.Save(vod).Error
}

// DeleteVod 删除视频
func DeleteVod(db *gorm.DB, vodID int) error {
	return db.Delete(&Vod{}, vodID).Error
}

// BatchCreateVods 批量创建视频
func BatchCreateVods(db *gorm.DB, vods []*Vod) error {
	return db.CreateInBatches(vods, 100).Error
}

// FindVodByName 通过名称查找视频
func FindVodByName(db *gorm.DB, name string) (*Vod, error) {
	var vod Vod
	result := db.Where("vod_name = ?", name).First(&vod)
	if result.Error != nil {
		return nil, result.Error
	}
	return &vod, nil
}

// FindVideosByTypeID 查找指定分类下的视频（包含子分类）
func FindVideosByTypeID(db *gorm.DB, typeID uint, limit, offset int) ([]Vod, error) {
	var vods []Vod

	// 获取该分类及其所有子分类的ID
	typeIDs := []uint{typeID}

	// 查找子分类
	var childTypes []*Type
	db.Where("type_pid = ?", typeID).Find(&childTypes)
	for _, childType := range childTypes {
		typeIDs = append(typeIDs, uint(childType.TypeID))
	}

	result := db.Where("type_id IN ? AND vod_status = ?", typeIDs, 1).
		Order("vod_time DESC").
		Limit(limit).
		Offset(offset).
		Find(&vods)

	if result.Error != nil {
		return nil, result.Error
	}

	return vods, nil
}

// CountVideosByTypeID 统计指定分类下的视频数量（包含子分类）
func CountVideosByTypeID(db *gorm.DB, typeID uint) (int64, error) {
	var count int64

	// 获取该分类及其所有子分类的ID
	typeIDs := []uint{typeID}

	// 查找子分类
	var childTypes []*Type
	db.Where("type_pid = ?", typeID).Find(&childTypes)
	for _, childType := range childTypes {
		typeIDs = append(typeIDs, uint(childType.TypeID))
	}

	result := db.Model(&Vod{}).Where("type_id IN ? AND vod_status = ?", typeIDs, 1).Count(&count)
	if result.Error != nil {
		return 0, result.Error
	}

	return count, nil
}

// FindRelatedVideos 查找相关视频（同分类的其他视频）
func FindRelatedVideos(db *gorm.DB, typeID uint, excludeID uint, limit int) ([]Vod, error) {
	var vods []Vod

	result := db.Where("type_id = ? AND vod_id != ? AND vod_status = ?", typeID, excludeID, 1).
		Order("vod_time DESC").
		Limit(limit).
		Find(&vods)

	if result.Error != nil {
		return nil, result.Error
	}

	return vods, nil
}

// SearchVideos 搜索视频
func SearchVideos(db *gorm.DB, keyword string, limit, offset int) ([]Vod, error) {
	var vods []Vod

	result := db.Where("(vod_name LIKE ? OR vod_actor LIKE ? OR vod_director LIKE ?) AND vod_status = ?",
		"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", 1).
		Order("vod_time DESC").
		Limit(limit).
		Offset(offset).
		Find(&vods)

	if result.Error != nil {
		return nil, result.Error
	}

	return vods, nil
}

// CountSearchVideos 统计搜索结果数量
func CountSearchVideos(db *gorm.DB, keyword string) (int64, error) {
	var count int64

	result := db.Model(&Vod{}).Where("(vod_name LIKE ? OR vod_actor LIKE ? OR vod_director LIKE ?) AND vod_status = ?",
		"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", 1).Count(&count)

	if result.Error != nil {
		return 0, result.Error
	}

	return count, nil
}
