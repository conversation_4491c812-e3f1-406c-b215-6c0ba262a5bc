package models

import (
	"sort"
	"time"

	"gorm.io/gorm"
)

// Type 分类信息模型
type Type struct {
	TypeID        int       `gorm:"column:type_id;primaryKey;autoIncrement" json:"type_id"`
	TypeName      string    `gorm:"column:type_name;size:50;index" json:"type_name"`
	TypeEn        string    `gorm:"column:type_en;size:50;index" json:"type_en"`
	TypeSort      int       `gorm:"column:type_sort;index;default:0" json:"type_sort"`
	TypePid       int       `gorm:"column:type_pid;index;default:0" json:"type_pid"`
	TypeStatus    int8      `gorm:"column:type_status;default:1" json:"type_status"`
	TypeDirection int8      `gorm:"column:type_direction;default:0" json:"type_direction"` // 0横屏,1竖屏
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// TableName 设置表名
func (Type) TableName() string {
	return "types"
}

// FindTypeByID 通过ID查找分类
func FindTypeByID(db *gorm.DB, typeID int) (*Type, error) {
	var typeInfo Type
	result := db.First(&typeInfo, typeID)
	if result.Error != nil {
		return nil, result.Error
	}
	return &typeInfo, nil
}

// FindTypesByPid 查找指定父分类下的所有子分类
func FindTypesByPid(db *gorm.DB, pid int) ([]*Type, error) {
	var types []*Type
	result := db.Where("type_pid = ?", pid).Order("type_sort DESC").Find(&types)
	if result.Error != nil {
		return nil, result.Error
	}
	return types, nil
}

// FindAllTypes 查找所有分类
func FindAllTypes(db *gorm.DB) ([]*Type, error) {
	var types []*Type
	result := db.Order("type_sort DESC").Find(&types)
	if result.Error != nil {
		return nil, result.Error
	}
	return types, nil
}

// TypeWithCount 带视频数量的分类信息
type TypeWithCount struct {
	Type
	VideoCount int `json:"video_count"`
}

// FindAllTypesWithCount 查找所有分类并包含每个分类的视频数量
func FindAllTypesWithCount(db *gorm.DB) ([]*TypeWithCount, error) {
	// 首先获取所有分类
	types, err := FindAllTypes(db)
	if err != nil {
		return nil, err
	}

	// 创建结果切片
	typesWithCount := make([]*TypeWithCount, len(types))

	// 遍历每个分类，获取其视频数量
	for i, t := range types {
		// 创建新的TypeWithCount对象
		typeWithCount := &TypeWithCount{Type: *t}

		// 查询该分类下的视频数量
		var count int64
		db.Model(&Vod{}).Where("type_id = ?", t.TypeID).Count(&count)

		// 设置视频数量
		typeWithCount.VideoCount = int(count)

		// 添加到结果切片
		typesWithCount[i] = typeWithCount
	}

	return typesWithCount, nil
}

// TypeWithChildren 带有子栏目的分类信息
type TypeWithChildren struct {
	TypeWithCount
	Children []*TypeWithChildren `json:"children"`
	Level    int                 `json:"level"`
}

// FindTypesHierarchy 查找分类的层级结构
func FindTypesHierarchy(db *gorm.DB) ([]*TypeWithChildren, error) {
	// 获取所有分类及其视频数量
	allTypes, err := FindAllTypesWithCount(db)
	if err != nil {
		return nil, err
	}

	// 创建ID到分类的映射，方便快速查找
	typeMap := make(map[int]*TypeWithCount)
	for _, t := range allTypes {
		typeMap[t.TypeID] = t
	}

	// 创建根节点列表（父ID为0的分类）
	var rootTypes []*TypeWithChildren

	// 创建ID到层级结构的映射
	hierarchyMap := make(map[int]*TypeWithChildren)

	// 首先创建所有TypeWithChildren对象
	for _, t := range allTypes {
		hierarchyMap[t.TypeID] = &TypeWithChildren{
			TypeWithCount: *t,
			Children:      []*TypeWithChildren{},
			Level:         0, // 初始化为0，后面会更新
		}
	}

	// 构建层级关系
	for _, t := range allTypes {
		node := hierarchyMap[t.TypeID]
		if t.TypePid == 0 {
			// 这是根节点
			rootTypes = append(rootTypes, node)
		} else if parent, exists := hierarchyMap[t.TypePid]; exists {
			// 添加到父节点的子节点列表
			parent.Children = append(parent.Children, node)
		} else {
			// 父节点不存在，作为根节点处理
			rootTypes = append(rootTypes, node)
		}
	}

	// 设置层级深度
	setLevels(rootTypes, 0)

	// 按排序字段排序
	sortTypeHierarchy(rootTypes)

	return rootTypes, nil
}

// setLevels 递归设置层级深度
func setLevels(types []*TypeWithChildren, level int) {
	for _, t := range types {
		t.Level = level
		setLevels(t.Children, level+1)
	}
}

// sortTypeHierarchy 递归排序层级结构
func sortTypeHierarchy(types []*TypeWithChildren) {
	// 按TypeSort降序排序
	sort.Slice(types, func(i, j int) bool {
		return types[i].TypeSort > types[j].TypeSort
	})

	// 递归排序子节点
	for _, t := range types {
		sortTypeHierarchy(t.Children)
	}
}

// CreateType 创建分类
func CreateType(db *gorm.DB, typeInfo *Type) error {
	return db.Create(typeInfo).Error
}

// UpdateType 更新分类
func UpdateType(db *gorm.DB, typeInfo *Type) error {
	return db.Save(typeInfo).Error
}

// DeleteType 删除分类
func DeleteType(db *gorm.DB, typeID int) error {
	return db.Delete(&Type{}, typeID).Error
}

// FindParentTypes 查找所有父级分类（pid=0）
func FindParentTypes(db *gorm.DB) ([]*Type, error) {
	var types []*Type
	result := db.Where("type_pid = ? AND type_status = ?", 0, 1).Order("type_sort DESC").Find(&types)
	if result.Error != nil {
		return nil, result.Error
	}
	return types, nil
}
