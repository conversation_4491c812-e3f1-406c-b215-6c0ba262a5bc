{{template "default/layout.html" .}}

{{define "title"}}{{.vod.VodName}} - 视频网站{{end}}

{{define "breadcrumb"}}
<div class="breadcrumb">
    <a href="/">首页</a> &gt;
    <a href="/type/{{.type.TypeID}}">{{.type.TypeName}}</a> &gt;
    <span>{{.vod.VodName}}</span>
</div>
{{end}}

{{define "content"}}
<div class="detail-page">
    <div class="video-detail">
        <div class="video-main">
            <div class="video-poster">
                {{if .vod.VodPic}}
                <img src="{{fixImagePath .vod.VodPic}}" alt="{{.vod.VodName}}">
                {{else}}
                <div class="no-image">暂无图片</div>
                {{end}}
            </div>

            <div class="video-info">
                <h1>{{.vod.VodName}}</h1>

                <div class="video-meta">
                    {{if .vod.VodRemarks}}<span class="tag">{{.vod.VodRemarks}}</span>{{end}}
                    {{if .vod.VodYear}}<span class="tag">{{.vod.VodYear}}</span>{{end}}
                    {{if .vod.VodArea}}<span class="tag">{{.vod.VodArea}}</span>{{end}}
                    {{if .vod.VodLang}}<span class="tag">{{.vod.VodLang}}</span>{{end}}
                </div>

                <div class="video-stats">
                    <div class="stat-item">
                        <span class="label">点击量:</span>
                        <span class="value">{{.vod.VodHits}}</span>
                    </div>
                    <div class="stat-item">
                        <span class="label">评分:</span>
                        <span class="value">{{.vod.VodScore}}</span>
                    </div>
                    <div class="stat-item">
                        <span class="label">更新时间:</span>
                        <span class="value">{{.vod.VodTime}}</span>
                    </div>
                </div>

                {{if .vod.VodDirector}}
                <div class="video-detail-item">
                    <span class="label">导演:</span>
                    <span class="value">{{.vod.VodDirector}}</span>
                </div>
                {{end}}

                {{if .vod.VodActor}}
                <div class="video-detail-item">
                    <span class="label">主演:</span>
                    <span class="value">{{.vod.VodActor}}</span>
                </div>
                {{end}}

                {{if .vod.VodContent}}
                <div class="video-description">
                    <h3>剧情简介</h3>
                    <p>{{.vod.VodContent}}</p>
                </div>
                {{end}}
            </div>
        </div>

        <!-- 播放列表 -->
        {{if .episodes}}
        <div class="episode-list">
            <h3>播放列表</h3>
            <div class="episodes">
                {{range .episodes}}
                <div class="episode-item">
                    <a href="{{.EpisodeUrl}}" target="_blank" class="episode-link">
                        {{.EpisodeName}}
                    </a>
                </div>
                {{end}}
            </div>
        </div>
        {{else if .vod.VodPlayUrl}}
        <div class="episode-list">
            <h3>播放地址</h3>
            <div class="episodes">
                <div class="episode-item">
                    <a href="{{.vod.VodPlayUrl}}" target="_blank" class="episode-link">
                        立即播放
                    </a>
                </div>
            </div>
        </div>
        {{end}}
    </div>
</div>
{{end}}

{{define "head"}}
<style>
.detail-page {
    max-width: 1200px;
    margin: 0 auto;
}

.video-detail {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.video-main {
    display: flex;
    gap: 30px;
    padding: 30px;
}

.video-poster {
    flex-shrink: 0;
    width: 300px;
}

.video-poster img {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

.video-poster .no-image {
    width: 100%;
    height: 400px;
    background: #f5f5f5;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 16px;
}

.video-info {
    flex: 1;
}

.video-info h1 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 28px;
}

.video-meta {
    margin-bottom: 20px;
}

.video-meta .tag {
    display: inline-block;
    background: #3498db;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 8px;
    margin-bottom: 5px;
}

.video-stats {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
    padding: 15px 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-item .label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.stat-item .value {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
}

.video-detail-item {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.video-detail-item .label {
    width: 80px;
    color: #666;
    font-weight: bold;
    flex-shrink: 0;
}

.video-detail-item .value {
    flex: 1;
    color: #333;
    line-height: 1.5;
}

.video-description {
    margin-top: 20px;
}

.video-description h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.video-description p {
    color: #666;
    line-height: 1.8;
}

.episode-list {
    padding: 30px;
    border-top: 1px solid #eee;
}

.episode-list h3 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.episodes {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
}

.episode-item {
    background: #f8f9fa;
    border-radius: 4px;
    overflow: hidden;
}

.episode-link {
    display: block;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s;
    font-size: 14px;
}

.episode-link:hover {
    background: #3498db;
    color: #fff;
}

@media (max-width: 768px) {
    .video-main {
        flex-direction: column;
        gap: 20px;
        padding: 20px;
    }

    .video-poster {
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }

    .video-stats {
        gap: 15px;
    }

    .episodes {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}
</style>
{{end}}
