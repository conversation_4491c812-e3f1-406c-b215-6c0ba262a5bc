package vod

import (
	"math"
	"net/http"
	"strconv"

	"awesomeProject7/models"

	"github.com/gin-gonic/gin"
)

// FrontController 前端控制器
type FrontController struct{}

// ShowIndex 显示首页
func (fc *FrontController) ShowIndex(c *gin.Context) {
	// 获取父级栏目（pid=0）
	parentTypes, err := models.FindParentTypes(models.DB)
	if err != nil {
		c.HTML(http.StatusOK, "home.html", gin.H{
			"error": "获取栏目失败",
		})
		return
	}

	// 为每个父级栏目获取最新的10个视频
	typeVideos := make(map[int][]models.Vod)
	for _, parentType := range parentTypes {
		// 获取该栏目及其子栏目的所有视频
		videos, err := models.FindVideosByTypeID(models.DB, uint(parentType.TypeID), 6, 0)
		if err == nil {
			typeVideos[parentType.TypeID] = videos
		}
	}

	c.HTML(http.StatusOK, "home.html", gin.H{
		"title":       "首页",
		"parentTypes": parentTypes,
		"typeVideos":  typeVideos,
	})
}

// ShowList 显示栏目列表页
func (fc *FrontController) ShowList(c *gin.Context) {
	// 获取栏目ID
	typeIDStr := c.Param("id")
	typeID, err := strconv.ParseUint(typeIDStr, 10, 32)
	if err != nil {
		c.HTML(http.StatusNotFound, "list.html", gin.H{
			"error": "无效的栏目ID",
		})
		return
	}

	// 获取栏目信息
	typeInfo, err := models.FindTypeByID(models.DB, int(typeID))
	if err != nil {
		c.HTML(http.StatusNotFound, "list.html", gin.H{
			"error": "栏目不存在",
		})
		return
	}

	// 获取分页参数
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	pageSize := 20
	offset := (page - 1) * pageSize

	// 获取视频列表
	videos, err := models.FindVideosByTypeID(models.DB, uint(typeID), pageSize, offset)
	if err != nil {
		c.HTML(http.StatusOK, "list.html", gin.H{
			"error":    "获取视频列表失败",
			"typeInfo": typeInfo,
		})
		return
	}

	// 获取总数用于分页
	totalCount, err := models.CountVideosByTypeID(models.DB, uint(typeID))
	if err != nil {
		totalCount = 0
	}

	totalPages := int(math.Ceil(float64(totalCount) / float64(pageSize)))

	c.HTML(http.StatusOK, "list.html", gin.H{
		"title":      typeInfo.TypeName + " - 列表",
		"typeInfo":   typeInfo,
		"videos":     videos,
		"page":       page,
		"totalPages": totalPages,
		"totalCount": totalCount,
	})
}

// ShowDetail 显示视频详情页
func (fc *FrontController) ShowDetail(c *gin.Context) {
	// 获取视频ID
	vodIDStr := c.Param("id")
	vodID, err := strconv.ParseUint(vodIDStr, 10, 32)
	if err != nil {
		c.HTML(http.StatusNotFound, "detail.html", gin.H{
			"error": "无效的视频ID",
		})
		return
	}

	// 获取视频信息
	video, err := models.FindVodByID(models.DB, int(vodID))
	if err != nil {
		c.HTML(http.StatusNotFound, "detail.html", gin.H{
			"error": "视频不存在",
		})
		return
	}

	// 获取栏目信息
	typeInfo, err := models.FindTypeByID(models.DB, video.TypeID)
	if err != nil {
		typeInfo = &models.Type{TypeName: "未知栏目"}
	}

	// 获取相关视频（同栏目的其他视频）
	relatedVideos, err := models.FindRelatedVideos(models.DB, uint(video.TypeID), uint(video.VodID), 10)
	if err != nil {
		relatedVideos = []models.Vod{}
	}

	c.HTML(http.StatusOK, "detail.html", gin.H{
		"title":         video.VodName + " - 详情",
		"video":         video,
		"typeInfo":      typeInfo,
		"relatedVideos": relatedVideos,
	})
}

// ShowSearch 显示搜索结果页
func (fc *FrontController) ShowSearch(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		c.HTML(http.StatusOK, "list.html", gin.H{
			"title": "搜索结果",
			"error": "请输入搜索关键词",
		})
		return
	}

	// 获取分页参数
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	pageSize := 20
	offset := (page - 1) * pageSize

	// 搜索视频
	videos, err := models.SearchVideos(models.DB, keyword, pageSize, offset)
	if err != nil {
		c.HTML(http.StatusOK, "list.html", gin.H{
			"title":   "搜索结果",
			"error":   "搜索失败",
			"keyword": keyword,
		})
		return
	}

	// 获取搜索结果总数
	totalCount, err := models.CountSearchVideos(models.DB, keyword)
	if err != nil {
		totalCount = 0
	}

	totalPages := int(math.Ceil(float64(totalCount) / float64(pageSize)))

	c.HTML(http.StatusOK, "list.html", gin.H{
		"title":      "搜索结果 - " + keyword,
		"videos":     videos,
		"keyword":    keyword,
		"page":       page,
		"totalPages": totalPages,
		"totalCount": totalCount,
		"isSearch":   true,
	})
}
